{"name": "multitenant-backend", "version": "1.0.0", "description": "Multi-tenant Node.js/Express backend with TypeScript", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "db:migrate": "npx sequelize-cli db:migrate", "db:seed": "npx sequelize-cli db:seed:all", "db:reset": "npx sequelize-cli db:drop && npx sequelize-cli db:create && npm run db:migrate && npm run db:seed", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["nodejs", "express", "typescript", "multitenant", "sequelize", "postgresql"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "sequelize": "^6.35.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "cookie-parser": "^1.4.6"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.5", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "jest": "^29.7.0", "@types/jest": "^29.5.11", "ts-jest": "^29.1.1", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "sequelize-cli": "^6.6.2"}, "engines": {"node": ">=18.0.0"}}