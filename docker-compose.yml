version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: multitenant_postgres
    environment:
      POSTGRES_DB: multitenant_app
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - app-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: multitenant_backend
    environment:
      NODE_ENV: development
      DATABASE_URL: ********************************************/multitenant_app
      JWT_SECRET: your-super-secret-jwt-key-change-in-production
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - app-network
    command: npm run dev

  # Frontend React App
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: multitenant_frontend
    environment:
      VITE_API_URL: http://localhost:3001
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - app-network
    command: npm run dev

volumes:
  postgres_data:

networks:
  app-network:
    driver: bridge
