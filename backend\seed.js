const { Sequelize, DataTypes } = require('sequelize');
const bcrypt = require('bcryptjs');

// Create database connection
const sequelize = new Sequelize({
  dialect: 'sqlite',
  storage: './database.sqlite',
  logging: false,
});

// Define models
const Tenant = sequelize.define('Tenant', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  subdomain: {
    type: DataTypes.STRING,
    allowNull: false,
    unique: true,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  settings: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
}, {
  tableName: 'tenants',
  timestamps: true,
});

const User = sequelize.define('User', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  email: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  password: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  firstName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  lastName: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  role: {
    type: DataTypes.ENUM('admin', 'user'),
    defaultValue: 'user',
  },
  tenantId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true,
  },
}, {
  tableName: 'users',
  timestamps: true,
});

const Project = sequelize.define('Project', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'completed'),
    defaultValue: 'active',
  },
  tenantId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  ownerId: {
    type: DataTypes.UUID,
    allowNull: false,
  },
  settings: {
    type: DataTypes.JSON,
    defaultValue: {},
  },
}, {
  tableName: 'projects',
  timestamps: true,
});

async function seedDatabase() {
  try {
    console.log('🌱 Seeding database...');

    // Create default tenant
    const defaultTenant = await Tenant.create({
      name: 'Default Tenant',
      subdomain: 'default',
      isActive: true,
      settings: {},
    });
    console.log('✅ Created default tenant');

    // Create demo tenant
    const demoTenant = await Tenant.create({
      name: 'Demo Company',
      subdomain: 'demo',
      isActive: true,
      settings: { theme: 'blue', features: ['projects', 'users'] },
    });
    console.log('✅ Created demo tenant');

    // Hash password
    const hashedPassword = await bcrypt.hash('admin123', 12);

    // Create admin user for default tenant
    const adminUser = await User.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'admin',
      tenantId: defaultTenant.id,
      isActive: true,
    });
    console.log('✅ Created admin user for default tenant');

    // Create demo user for demo tenant
    const demoUser = await User.create({
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Demo',
      lastName: 'User',
      role: 'user',
      tenantId: demoTenant.id,
      isActive: true,
    });
    console.log('✅ Created demo user for demo tenant');

    // Create sample projects for demo tenant
    await Project.create({
      name: 'Sample Project 1',
      description: 'This is a sample project for demonstration purposes',
      status: 'active',
      tenantId: demoTenant.id,
      ownerId: demoUser.id,
      settings: { priority: 'high', category: 'development' },
    });

    await Project.create({
      name: 'Sample Project 2',
      description: 'Another sample project with completed status',
      status: 'completed',
      tenantId: demoTenant.id,
      ownerId: demoUser.id,
      settings: { priority: 'medium', category: 'design' },
    });
    console.log('✅ Created sample projects');

    console.log('🎉 Database seeded successfully!');
    console.log('\n📋 Demo Credentials:');
    console.log('Admin: <EMAIL> / admin123');
    console.log('Demo User: <EMAIL> / admin123');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await sequelize.close();
  }
}

seedDatabase();
