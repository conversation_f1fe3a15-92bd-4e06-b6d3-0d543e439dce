# Multi-Tenant Web Application Starter

A comprehensive starter project for building multi-tenant web applications with Node.js/Express backend and React frontend.

## Architecture Overview

### Backend (Node.js + Express + TypeScript)
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Sequelize ORM
- **Multi-tenancy**: Subdomain-based tenant identification with shared database schema
- **Architecture**: Clean code architecture (routes, controllers, services, models)
- **Authentication**: JWT-based auth with tenant context

### Frontend (React + TypeScript)
- **Framework**: React with TypeScript (Vite)
- **State Management**: React Query for server state
- **HTTP Client**: Axios
- **Routing**: React Router with multi-tenant support

## Project Structure

```
├── backend/                 # Node.js/Express API
│   ├── src/
│   │   ├── config/         # Configuration files
│   │   ├── controllers/    # Request handlers
│   │   ├── middleware/     # Custom middleware
│   │   ├── models/         # Database models
│   │   ├── routes/         # API routes
│   │   ├── services/       # Business logic
│   │   └── types/          # TypeScript type definitions
│   ├── Dockerfile
│   └── package.json
├── frontend/               # React application
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   ├── hooks/          # Custom hooks
│   │   └── types/          # TypeScript interfaces
│   ├── Dockerfile
│   └── package.json
├── docker-compose.yml      # Multi-service Docker setup
└── README.md
```

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Docker (optional)

### Using Docker (Recommended)

1. Clone the repository
2. Copy environment files:
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```
3. Start all services:
   ```bash
   docker-compose up -d
   ```

### Manual Setup

#### Backend Setup
```bash
cd backend
npm install
cp .env.example .env
# Configure your database in .env
npm run dev
```

#### Frontend Setup
```bash
cd frontend
npm install
cp .env.example .env
npm run dev
```

## Multi-Tenancy

This application supports multi-tenancy through:

1. **Subdomain-based tenant identification**: `tenant1.localhost:3000`
2. **Fallback header-based**: `X-Tenant-ID` header
3. **Shared database with tenant isolation**: All tables include `tenant_id` column
4. **Automatic tenant context**: Sequelize automatically filters by tenant

## API Endpoints

### Authentication
- `POST /api/auth/login` - Login with email/password
- `POST /api/auth/logout` - Logout
- `GET /api/auth/me` - Get current user

### Users
- `GET /api/users` - List users (tenant-scoped)
- `GET /api/users/:id` - Get user details

### Projects
- `GET /api/projects` - List projects (tenant-scoped)
- `POST /api/projects` - Create project
- `PUT /api/projects/:id` - Update project
- `DELETE /api/projects/:id` - Delete project

## Environment Variables

See `.env.example` files in both backend and frontend directories for required configuration.

## Development

- Backend runs on `http://localhost:3001`
- Frontend runs on `http://localhost:3000`
- Database runs on `localhost:5432`

## Testing Multi-tenancy

1. Add entries to your hosts file:
   ```
   127.0.0.1 tenant1.localhost
   127.0.0.1 tenant2.localhost
   ```

2. Access different tenants:
   - `http://tenant1.localhost:3000`
   - `http://tenant2.localhost:3000`

## License

MIT
